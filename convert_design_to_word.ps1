# PowerShell脚本：将Markdown设计书转换为Word文档
# 使用方法：右键点击此文件，选择"使用PowerShell运行"

# 设置文件路径
$markdownFile = "CIMC_Design_Document_2025708112.md"
$wordFile = "CIMC设计书_成都纺织高等专科学校_2025708112.docx"

Write-Host "开始转换设计书文档..." -ForegroundColor Green

try {
    # 检查Markdown文件是否存在
    if (-not (Test-Path $markdownFile)) {
        Write-Host "错误：找不到源文件 $markdownFile" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit
    }

    # 创建Word应用程序对象
    $word = New-Object -ComObject Word.Application
    $word.Visible = $false

    # 创建新文档
    $doc = $word.Documents.Add()

    # 读取Markdown内容
    $content = Get-Content $markdownFile -Encoding UTF8 -Raw

    # 基本的Markdown到Word转换
    # 替换标题格式
    $content = $content -replace '^# (.+)$', '$1'
    $content = $content -replace '^## (.+)$', '$1'
    $content = $content -replace '^### (.+)$', '$1'
    
    # 替换代码块
    $content = $content -replace '```[\s\S]*?```', '[代码块]'
    $content = $content -replace '`([^`]+)`', '$1'
    
    # 替换粗体和斜体
    $content = $content -replace '\*\*([^*]+)\*\*', '$1'
    $content = $content -replace '\*([^*]+)\*', '$1'

    # 插入内容到Word文档
    $selection = $word.Selection
    $selection.TypeText($content)

    # 设置文档格式
    $doc.Range().Font.Name = "微软雅黑"
    $doc.Range().Font.Size = 12

    # 保存文档
    $fullPath = Join-Path (Get-Location) $wordFile
    $doc.SaveAs([ref]$fullPath, [ref]16) # 16 = Word文档格式

    # 关闭文档和Word应用程序
    $doc.Close()
    $word.Quit()

    Write-Host "转换完成！" -ForegroundColor Green
    Write-Host "输出文件：$wordFile" -ForegroundColor Yellow
    
    # 释放COM对象
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($word) | Out-Null
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()

} catch {
    Write-Host "转换过程中出现错误：$($_.Exception.Message)" -ForegroundColor Red
    if ($word) {
        $word.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($word) | Out-Null
    }
}

Write-Host "按任意键退出..." -ForegroundColor Cyan
Read-Host
