--cpu=Cortex-M4.fp.sp
"gd32_wangui\startup_stm32f429xx.o"
"gd32_wangui\main.o"
"gd32_wangui\gpio.o"
"gd32_wangui\adc.o"
"gd32_wangui\dac.o"
"gd32_wangui\dma.o"
"gd32_wangui\i2c.o"
"gd32_wangui\rtc.o"
"gd32_wangui\sdio.o"
"gd32_wangui\spi.o"
"gd32_wangui\tim.o"
"gd32_wangui\usart.o"
"gd32_wangui\stm32f4xx_it.o"
"gd32_wangui\stm32f4xx_hal_msp.o"
"gd32_wangui\bsp_driver_sd.o"
"gd32_wangui\sd_diskio.o"
"gd32_wangui\fatfs.o"
"gd32_wangui\diskio.o"
"gd32_wangui\ff.o"
"gd32_wangui\ff_gen_drv.o"
"gd32_wangui\syscall.o"
"gd32_wangui\cc936.o"
"gd32_wangui\stm32f4xx_hal_adc.o"
"gd32_wangui\stm32f4xx_hal_adc_ex.o"
"gd32_wangui\stm32f4xx_ll_adc.o"
"gd32_wangui\stm32f4xx_hal_rcc.o"
"gd32_wangui\stm32f4xx_hal_rcc_ex.o"
"gd32_wangui\stm32f4xx_hal_flash.o"
"gd32_wangui\stm32f4xx_hal_flash_ex.o"
"gd32_wangui\stm32f4xx_hal_flash_ramfunc.o"
"gd32_wangui\stm32f4xx_hal_gpio.o"
"gd32_wangui\stm32f4xx_hal_dma_ex.o"
"gd32_wangui\stm32f4xx_hal_dma.o"
"gd32_wangui\stm32f4xx_hal_pwr.o"
"gd32_wangui\stm32f4xx_hal_pwr_ex.o"
"gd32_wangui\stm32f4xx_hal_cortex.o"
"gd32_wangui\stm32f4xx_hal.o"
"gd32_wangui\stm32f4xx_hal_exti.o"
"gd32_wangui\stm32f4xx_hal_dac.o"
"gd32_wangui\stm32f4xx_hal_dac_ex.o"
"gd32_wangui\stm32f4xx_hal_i2c.o"
"gd32_wangui\stm32f4xx_hal_i2c_ex.o"
"gd32_wangui\stm32f4xx_hal_rtc.o"
"gd32_wangui\stm32f4xx_hal_rtc_ex.o"
"gd32_wangui\stm32f4xx_ll_sdmmc.o"
"gd32_wangui\stm32f4xx_hal_sd.o"
"gd32_wangui\stm32f4xx_hal_mmc.o"
"gd32_wangui\stm32f4xx_hal_spi.o"
"gd32_wangui\stm32f4xx_hal_tim.o"
"gd32_wangui\stm32f4xx_hal_tim_ex.o"
"gd32_wangui\stm32f4xx_hal_uart.o"
"gd32_wangui\system_stm32f4xx.o"
"gd32_wangui\ebtn.o"
"gd32_wangui\oled.o"
"gd32_wangui\ringbuffer.o"
"gd32_wangui\gd25qxx.o"
"gd32_wangui\lfs.o"
"gd32_wangui\lfs_port.o"
"gd32_wangui\lfs_util.o"
"..\Middlewares\ST\ARM\DSP\Lib\arm_cortexM4l_math.lib"
"gd32_wangui\adc_app.o"
"gd32_wangui\btn_app.o"
"gd32_wangui\data_storage.o"
"gd32_wangui\config_manager.o"
"gd32_wangui\device_id.o"
"gd32_wangui\led_app.o"
"gd32_wangui\oled_app.o"
"gd32_wangui\rtc_app.o"
"gd32_wangui\sampling_control.o"
"gd32_wangui\scheduler.o"
"gd32_wangui\system_check.o"
"gd32_wangui\usart_app.o"
--library_type=microlib --strict --scatter "GD32_Wangui\GD32_Wangui.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "GD32_Wangui.map" -o GD32_Wangui\GD32_Wangui.axf