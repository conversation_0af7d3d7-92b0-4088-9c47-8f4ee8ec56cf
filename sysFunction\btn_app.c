#include "btn_app.h"
#include "ebtn.h"
#include "gpio.h"

extern uint8_t ucLed[6];

typedef enum
{
    USER_BUTTON_0 = 0,
    USER_BUTTON_1,
    USER_BUTTON_2,
    USER_BUTTON_3,
    USER_BUTTON_4,
    USER_BUTTON_5,
    USER_BUTTON_MAX,
} user_button_t;

static const ebtn_btn_param_t defaul_ebtn_param = EBTN_PARAMS_INIT(20, 0, 20, 1000, 0, 1000, 10);

static ebtn_btn_t btns[] = {
    EBTN_BUTTON_INIT(USER_BUTTON_0, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_1, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_2, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_3, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_4, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_5, &defaul_ebtn_param),
};

uint8_t get_btn_state(struct ebtn_btn *btn)
{
    if (btn->key_id >= USER_BUTTON_MAX) return 0;

    GPIO_PinState pin_state;

    switch (btn->key_id)
    {
    case USER_BUTTON_0:
        pin_state = HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_15);
        break;
    case USER_BUTTON_1:
        pin_state = HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_13);
        break;
    case USER_BUTTON_2:
        pin_state = HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_11);
        break;
    case USER_BUTTON_3:
        pin_state = HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_9);
        break;
    case USER_BUTTON_4:
        pin_state = HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_7);
        break;
    case USER_BUTTON_5:
        pin_state = HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_0);
        break;
    default:
        return 0;
    }

    return (pin_state == GPIO_PIN_RESET) ? 1 : 0;
}

void handle_button_press_event(struct ebtn_btn *btn, ebtn_evt_t evt)
{
    if (evt != EBTN_EVT_ONPRESS) return;

    if (!btn || btn->key_id >= USER_BUTTON_MAX) return;

    switch (btn->key_id)
    {
        case USER_BUTTON_0:
            sampling_init();

            if (sampling_get_state() == SAMPLING_IDLE)
            {
                if (sampling_start() == SAMPLING_OK)
                {
                    sampling_cycle_t cycle = sampling_get_cycle();
                    my_printf(&huart1, "Periodic Sampling\r\n");
                    my_printf(&huart1, "sample cycle: %ds\r\n", (int)cycle);

                    extern uint8_t g_sampling_output_enabled;
                    extern uint32_t g_last_output_time;
                    g_sampling_output_enabled = 1;
                    g_last_output_time = HAL_GetTick();

                    char log_msg[64];
                    sprintf(log_msg, "sample start - cycle %ds (key1)", (int)cycle);
                    data_storage_write_log(log_msg);
                }
                else
                {
                    my_printf(&huart1, "sampling start failed.\r\n");
                }
            }
            else
            {
                if (sampling_stop() == SAMPLING_OK)
                {
                    my_printf(&huart1, "Periodic Sampling STOP\r\n");

                    extern uint8_t g_sampling_output_enabled;
                    g_sampling_output_enabled = 0;

                    data_storage_write_log("sample stop (key1)");
                }
                else
                {
                    my_printf(&huart1, "sampling stop failed.\r\n");
                }
            }
            break;
        case USER_BUTTON_1:
            sampling_init();
            if (sampling_set_cycle(CYCLE_5S) == SAMPLING_OK)
            {
                my_printf(&huart1, "sample cycle adjust:5s\r\n");

                data_storage_write_log("cycle adjust to 5s (key3)");
            }
            else
            {
                my_printf(&huart1, "cycle adjust failed.\r\n");
            }
            break;
        case USER_BUTTON_2:
            sampling_init();
            if (sampling_set_cycle(CYCLE_10S) == SAMPLING_OK)
            {
                my_printf(&huart1, "sample cycle adjust:10s\r\n");

                data_storage_write_log("cycle adjust to 10s (key2)");
            }
            else
            {
                my_printf(&huart1, "cycle adjust failed.\r\n");
            }
            break;
        case USER_BUTTON_3:
            sampling_init();
            if (sampling_set_cycle(CYCLE_15S) == SAMPLING_OK)
            {
                my_printf(&huart1, "sample cycle adjust:15s\r\n");

                data_storage_write_log("cycle adjust to 15s (key4)");
            }
            else
            {
                my_printf(&huart1, "cycle adjust failed.\r\n");
            }
            break;
        case USER_BUTTON_4:
            data_storage_write_log("key4 pressed");
            break;
        case USER_BUTTON_5:
            break;
        default:
            break;
        }
}

void app_btn_init(void)
{
    ebtn_init(btns, EBTN_ARRAY_SIZE(btns), NULL, 0, get_btn_state, handle_button_press_event);
    HAL_TIM_Base_Start_IT(&htim14);
}

void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
}

void btn_task(void)
{
    ebtn_process(uwTick);
}
