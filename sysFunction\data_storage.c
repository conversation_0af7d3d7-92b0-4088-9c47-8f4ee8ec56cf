#include "data_storage.h"
#include "fatfs.h"
#include "rtc_app.h"
#include "usart_app.h"
#include "string.h"
#include "stdio.h"

extern FATFS SDFatFS;
extern char SDPath[4];
extern RTC_HandleTypeDef hrtc;
extern UART_HandleTypeDef huart1;

extern uint32_t convert_rtc_to_unix_timestamp(RTC_TimeTypeDef *time, RTC_DateTypeDef *date);
extern void format_hex_output(uint32_t timestamp, float voltage, uint8_t is_overlimit, char *output);

static file_state_t g_file_states[STORAGE_TYPE_COUNT];
static uint32_t g_boot_count = 0;

static internal_storage_state_t g_internal_storage_state = INTERNAL_STORAGE_DISABLED;
static char g_internal_log_buffer[INTERNAL_LOG_BUFFER_SIZE];
static uint16_t g_internal_log_count = 0;
static uint16_t g_internal_log_write_pos = 0;

static const char *g_directory_names[STORAGE_TYPE_COUNT] = {
    "sample",
    "overLimit",
    "log",
    "hideData"
};

static const char *g_filename_prefixes[STORAGE_TYPE_COUNT] = {
    "sampleData",
    "overLimit",
    "log",
    "hideData"
};

static uint32_t get_boot_count_from_fatfs(void)
{
    FIL file;
    uint32_t boot_count = 0;
    UINT bytes_read;
    FRESULT res;

    res = f_open(&file, "boot_count.txt", FA_READ);
    if (res == FR_OK)
    {
        res = f_read(&file, &boot_count, sizeof(boot_count), &bytes_read);
        if (res != FR_OK || bytes_read != sizeof(boot_count))
        {
            boot_count = 0;
        }
        f_close(&file);
    }

    return boot_count;
}

static data_storage_status_t save_boot_count_to_fatfs(uint32_t boot_count)
{
    FIL file;
    UINT bytes_written;
    FRESULT res;

    res = f_open(&file, "boot_count.txt", FA_CREATE_ALWAYS | FA_WRITE);
    if (res != FR_OK)
    {
        return DATA_STORAGE_ERROR;
    }

    res = f_write(&file, &boot_count, sizeof(boot_count), &bytes_written);
    if (res != FR_OK || bytes_written != sizeof(boot_count))
    {
        f_close(&file);
        return DATA_STORAGE_ERROR;
    }

    f_close(&file);
    return DATA_STORAGE_OK;
}



static data_storage_status_t create_storage_directories(void) // 创建存储目录 参数:无 返回:操作状态
{
    FRESULT res;                  // 操作结果
    uint8_t success_count = 0;    // 成功计数

    for (uint8_t i = 0; i < STORAGE_TYPE_COUNT; i++) // 创建四个必要目录
    {
        res = f_mkdir(g_directory_names[i]);          // 创建目录
        if (res == FR_OK)                             // 创建成功
        {
            // my_printf(&huart1, "Created directory: %s\r\n", g_directory_names[i]);
            success_count++;
        }
        else if (res == FR_EXIST) // 目录已存在
        {
            // my_printf(&huart1, "Directory already exists: %s\r\n", g_directory_names[i]);
            success_count++;
        }
        else // 创建失败
        {
            my_printf(&huart1, "Failed to create directory %s, error: %d\r\n", g_directory_names[i], res);
        }
    }

    return (success_count == STORAGE_TYPE_COUNT) ? DATA_STORAGE_OK : DATA_STORAGE_ERROR; // 所有目录都创建成功或已存在，返回成功
}

data_storage_status_t data_storage_init(void) // 初始化存储系统 参数:无 返回:操作状态
{
    memset(g_file_states, 0, sizeof(g_file_states)); // 清空文件状态数组

    // my_printf(&huart1, "Initializing data storage system...\r\n");

    FRESULT mount_result = f_mount(&SDFatFS, SDPath, 1); // 挂载SD卡文件系统
    if (mount_result != FR_OK)                           // 挂载失败处理
    {
        // SD卡不可用，启用内部存储模式
        // my_printf(&huart1, "SD card not available, enabling internal storage mode\r\n");
        internal_storage_init();
        return DATA_STORAGE_NO_SD;
    }

    // my_printf(&huart1, "SD card filesystem mounted successfully\r\n");

    data_storage_status_t dir_result = create_storage_directories(); // 创建必要的目录结构
    if (dir_result != DATA_STORAGE_OK)                               // 目录创建失败处理
    {
        my_printf(&huart1, "Warning: Some directories creation failed, system may not work properly\r\n");
        // 继续运行，但系统可能不正常，仅输出警告
    }

    // 临时重置：如果您想从log0开始，取消下面两行的注释并注释掉后面两行
    // g_boot_count = 0;                           // 重置为0
    // g_boot_count++;                             // 递增为1，创建log0.txt

    // 正常模式：连续计数
    g_boot_count = get_boot_count_from_fatfs(); // 获取开机计数
    g_boot_count++;                             // 递增开机计数

    data_storage_status_t boot_result = save_boot_count_to_fatfs(g_boot_count); // 保存新的开机计数
    if (boot_result != DATA_STORAGE_OK)                                         // 保存失败处理
    {
        // my_printf(&huart1, "Warning: Failed to save boot count\r\n");
    }

    // my_printf(&huart1, "Data storage system initialized, boot count: %lu, log file: log%lu.txt\r\n", g_boot_count, g_boot_count); // 输出初始化信息

    return DATA_STORAGE_OK;
}

static data_storage_status_t check_and_update_filename(storage_type_t type) // 检查并更新文件名 参数:存储类型 返回:操作状态
{
    if (type >= STORAGE_TYPE_COUNT) // 参数验证
    {
        return DATA_STORAGE_INVALID;
    }

    file_state_t *state = &g_file_states[type]; // 获取文件状态指针

    if (state->data_count >= DATA_STORAGE_MAX_RECORDS_PER_FILE || !state->file_exists) // 检查是否需要生成新文件名(达到最大记录数或文件不存在)
    {
        char filename[64]; // 文件名缓冲区
        data_storage_status_t result = generate_filename(type, filename); // 生成新文件名
        if (result != DATA_STORAGE_OK)
        {
            return result;
        }

        strcpy(state->current_filename, filename); // 更新文件状态
        state->data_count = 0;
        state->file_exists = 1; // 标记文件已准备
        state->file_opened = 1; // 兼容性保留
    }

    return DATA_STORAGE_OK;
}

static data_storage_status_t write_data_to_file(storage_type_t type, const char *data) // 通用文件写入函数 参数:存储类型,数据字符串 返回:操作状态
{
    if (type >= STORAGE_TYPE_COUNT || data == NULL) // 参数验证
    {
        return DATA_STORAGE_INVALID;
    }

    data_storage_status_t result = check_and_update_filename(type); // 检查并更新文件名
    if (result != DATA_STORAGE_OK)
    {
        return result;
    }

    file_state_t *state = &g_file_states[type]; // 获取文件状态指针
    char full_path[96]; // 完整路径缓冲区
    sprintf(full_path, "%s/%s", g_directory_names[type], state->current_filename); // 构造完整路径

    FIL file_handle; // 文件句柄
    FRESULT res = f_open(&file_handle, full_path, FA_OPEN_ALWAYS | FA_WRITE); // 打开已存在文件或创建新文件
    if (res != FR_OK)
    {
        return DATA_STORAGE_ERROR;
    }

    // 移动到文件末尾(追加模式)
    res = f_lseek(&file_handle, f_size(&file_handle));
    if (res != FR_OK)
    {
        f_close(&file_handle);
        return DATA_STORAGE_ERROR;
    }

    UINT bytes_written;                                                           // 写入字节数
    res = f_write(&file_handle, data, strlen(data), &bytes_written);             // 写入数据
    if (res != FR_OK || bytes_written != strlen(data))                           // 写入失败检查
    {
        f_close(&file_handle);
        return DATA_STORAGE_ERROR;
    }

    res = f_write(&file_handle, "\n", 1, &bytes_written); // 写入换行符
    if (res != FR_OK || bytes_written != 1)               // 换行符写入失败检查
    {
        f_close(&file_handle);
        return DATA_STORAGE_ERROR;
    }

    f_sync(&file_handle); // 同步数据到SD卡
    f_close(&file_handle); // 关闭文件

    state->data_count++; // 增加数据计数

    return DATA_STORAGE_OK;
}


static data_storage_status_t format_sample_data(float voltage, char *formatted_data)
{
    if (formatted_data == NULL)
    {
        return DATA_STORAGE_INVALID;
    }


    RTC_TimeTypeDef current_rtc_time = {0};
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);


    sprintf(formatted_data, "%04d-%02d-%02d %02d:%02d:%02d %.4fV",
            current_rtc_date.Year + 2000,
            current_rtc_date.Month,
            current_rtc_date.Date,
            current_rtc_time.Hours,
            current_rtc_time.Minutes,
            current_rtc_time.Seconds,
            voltage);

    return DATA_STORAGE_OK;
}

data_storage_status_t data_storage_write_sample(float voltage) // 写入采样数据 参数:电压值 返回:操作状态
{
    char formatted_data[128]; // 格式化数据缓冲区

    data_storage_status_t result = format_sample_data(voltage, formatted_data); // 格式化数据
    if (result != DATA_STORAGE_OK)
    {
        return result;
    }

    return write_data_to_file(STORAGE_SAMPLE, formatted_data); // 写入文件
}


static data_storage_status_t format_overlimit_data(float voltage, float limit, char *formatted_data)
{
    if (formatted_data == NULL)
    {
        return DATA_STORAGE_INVALID;
    }


    RTC_TimeTypeDef current_rtc_time = {0};
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);


    sprintf(formatted_data, "%04d-%02d-%02d %02d:%02d:%02d %.4fV limit %.4fV",
            current_rtc_date.Year + 2000,
            current_rtc_date.Month,
            current_rtc_date.Date,
            current_rtc_time.Hours,
            current_rtc_time.Minutes,
            current_rtc_time.Seconds,
            voltage,
            limit);

    return DATA_STORAGE_OK;
}

data_storage_status_t data_storage_write_overlimit(float voltage, float limit) // 写入超限数据 参数:电压值,阈值 返回:操作状态
{
    char formatted_data[128]; // 格式化数据缓冲区

    data_storage_status_t result = format_overlimit_data(voltage, limit, formatted_data); // 格式化数据
    if (result != DATA_STORAGE_OK)
    {
        return result;
    }

    return write_data_to_file(STORAGE_OVERLIMIT, formatted_data); // 写入文件
}


static data_storage_status_t format_log_data(const char *operation, char *formatted_data)
{
    if (formatted_data == NULL || operation == NULL)
    {
        return DATA_STORAGE_INVALID;
    }

    RTC_TimeTypeDef current_rtc_time = {0};
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);

    sprintf(formatted_data, "%04d-%02d-%02d %02d:%02d:%02d %s",
            current_rtc_date.Year + 2000,
            current_rtc_date.Month,
            current_rtc_date.Date,
            current_rtc_time.Hours,
            current_rtc_time.Minutes,
            current_rtc_time.Seconds,
            operation);

    return DATA_STORAGE_OK;
}

data_storage_status_t data_storage_write_log(const char *operation) // 写入日志数据 参数:操作描述 返回:操作状态
{
    // 如果内部存储激活，写入到内部存储
    if (g_internal_storage_state == INTERNAL_STORAGE_ACTIVE)
    {
        return internal_storage_write_log(operation);
    }

    // 否则写入到SD卡
    char formatted_data[256]; // 格式化数据缓冲区

    data_storage_status_t result = format_log_data(operation, formatted_data); // 格式化数据
    if (result != DATA_STORAGE_OK)
    {
        return result;
    }

    return write_data_to_file(STORAGE_LOG, formatted_data); // 写入文件
}


static data_storage_status_t format_hidedata(float voltage, uint8_t is_overlimit, char *formatted_data)
{
    if (formatted_data == NULL)
    {
        return DATA_STORAGE_INVALID;
    }


    RTC_TimeTypeDef current_rtc_time = {0};
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);


    char original_line[128];
    sprintf(original_line, "%04d-%02d-%02d %02d:%02d:%02d %.4fV",
            current_rtc_date.Year + 2000,
            current_rtc_date.Month,
            current_rtc_date.Date,
            current_rtc_time.Hours,
            current_rtc_time.Minutes,
            current_rtc_time.Seconds,
            voltage);

    
    uint32_t timestamp = convert_rtc_to_unix_timestamp(&current_rtc_time, &current_rtc_date);
    char hex_output[32];
    format_hex_output(timestamp, voltage, is_overlimit, hex_output);

    sprintf(formatted_data, "%s\nhide: %s", original_line, hex_output);

    return DATA_STORAGE_OK;
}

data_storage_status_t data_storage_write_hidedata(float voltage, uint8_t is_overlimit) // 写入隐藏数据 参数:电压值,超限标志 返回:操作状态
{
    char formatted_data[256]; // 格式化数据缓冲区

    data_storage_status_t result = format_hidedata(voltage, is_overlimit, formatted_data); // 格式化数据
    if (result != DATA_STORAGE_OK)
    {
        return result;
    }

    return write_data_to_file(STORAGE_HIDEDATA, formatted_data); // 写入文件，hideData格式包含两行，但数据计数仍按2条计算
}

data_storage_status_t generate_datetime_string(char *datetime_str) // 生成datetime字符串 参数:字符串缓冲区 返回:操作状态
{
    if (datetime_str == NULL) // 参数验证
    {
        return DATA_STORAGE_INVALID;
    }

    RTC_TimeTypeDef current_rtc_time = {0}; // 获取当前RTC时间
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);

    sprintf(datetime_str, "%04d%02d%02d%02d%02d%02d", // 转换为YYYYMMDDHHMMSS格式(14位数字)
            current_rtc_date.Year + 2000,             // 转换为4位年份
            current_rtc_date.Month,
            current_rtc_date.Date,
            current_rtc_time.Hours,
            current_rtc_time.Minutes,
            current_rtc_time.Seconds);

    return DATA_STORAGE_OK;
}

data_storage_status_t generate_filename(storage_type_t type, char *filename) // 生成文件名 参数:存储类型,文件名缓冲区 返回:操作状态
{
    if (filename == NULL || type >= STORAGE_TYPE_COUNT) // 参数验证
    {
        return DATA_STORAGE_INVALID;
    }

    if (type == STORAGE_LOG) // log文件使用上电次数命名：log1.txt, log2.txt, log3.txt, ...
    {
        // 使用boot_count作为文件序号，从log1.txt开始
        sprintf(filename, "%s%lu.txt", g_filename_prefixes[type], g_boot_count);
    }
    else // 其他文件使用datetime命名：{prefix}{datetime}.txt
    {
        char datetime_str[16];                                                  // 时间字符串缓冲区
        data_storage_status_t result = generate_datetime_string(datetime_str); // 生成时间字符串
        if (result != DATA_STORAGE_OK)
        {
            return result;
        }
        sprintf(filename, "%s%s.txt", g_filename_prefixes[type], datetime_str);
    }

    return DATA_STORAGE_OK;
}

data_storage_status_t data_storage_test(void) // 测试存储系统 参数:无 返回:操作状态
{
    my_printf(&huart1, "Data storage system test - placeholder\r\n");
    return DATA_STORAGE_OK;
}



// 内部存储初始化函数
data_storage_status_t internal_storage_init(void)
{
    // 清空内部存储缓冲区
    memset(g_internal_log_buffer, 0, sizeof(g_internal_log_buffer));
    g_internal_log_count = 0;
    g_internal_log_write_pos = 0;
    g_internal_storage_state = INTERNAL_STORAGE_ACTIVE;

    return DATA_STORAGE_OK;
}

// 写入log到内部存储
data_storage_status_t internal_storage_write_log(const char *operation)
{
    if (operation == NULL || g_internal_storage_state != INTERNAL_STORAGE_ACTIVE)
    {
        return DATA_STORAGE_INVALID;
    }

    // 检查是否还有空间
    if (g_internal_log_count >= INTERNAL_LOG_MAX_ENTRIES)
    {
        return DATA_STORAGE_FULL; // 内部存储已满
    }

    // 格式化log数据
    char formatted_data[INTERNAL_LOG_ENTRY_SIZE];
    RTC_TimeTypeDef current_rtc_time = {0};
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);

    snprintf(formatted_data, sizeof(formatted_data), "%04d-%02d-%02d %02d:%02d:%02d %s",
             current_rtc_date.Year + 2000,
             current_rtc_date.Month,
             current_rtc_date.Date,
             current_rtc_time.Hours,
             current_rtc_time.Minutes,
             current_rtc_time.Seconds,
             operation);

    // 写入到内部缓冲区
    uint16_t entry_start = g_internal_log_count * INTERNAL_LOG_ENTRY_SIZE;
    strncpy(&g_internal_log_buffer[entry_start], formatted_data, INTERNAL_LOG_ENTRY_SIZE - 1);
    g_internal_log_buffer[entry_start + INTERNAL_LOG_ENTRY_SIZE - 1] = '\0'; // 确保字符串结束

    g_internal_log_count++;

    return DATA_STORAGE_OK;
}

// 将内部存储的数据转移到SD卡
data_storage_status_t internal_storage_transfer_to_sd(void)
{
    if (g_internal_storage_state != INTERNAL_STORAGE_ACTIVE || g_internal_log_count == 0)
    {
        return DATA_STORAGE_OK; // 没有数据需要转移
    }

    // 检查SD卡是否可用
    FRESULT mount_result = f_mount(&SDFatFS, SDPath, 1);
    if (mount_result != FR_OK)
    {
        return DATA_STORAGE_NO_SD; // SD卡不可用
    }

    g_internal_storage_state = INTERNAL_STORAGE_TRANSFERRING;

    // 确保目录存在
    data_storage_status_t dir_result = create_storage_directories();
    if (dir_result != DATA_STORAGE_OK)
    {
        g_internal_storage_state = INTERNAL_STORAGE_ACTIVE;
        return DATA_STORAGE_ERROR;
    }

    // 创建log1.txt文件
    char log_filename[32];
    sprintf(log_filename, "log/log%lu.txt", g_boot_count);

    FIL log_file;
    FRESULT res = f_open(&log_file, log_filename, FA_CREATE_ALWAYS | FA_WRITE);
    if (res != FR_OK)
    {
        g_internal_storage_state = INTERNAL_STORAGE_ACTIVE;
        return DATA_STORAGE_ERROR;
    }

    // 写入所有内部存储的log数据
    for (uint16_t i = 0; i < g_internal_log_count; i++)
    {
        uint16_t entry_start = i * INTERNAL_LOG_ENTRY_SIZE;
        char *log_entry = &g_internal_log_buffer[entry_start];

        if (strlen(log_entry) > 0) // 确保有有效数据
        {
            UINT bytes_written;
            res = f_write(&log_file, log_entry, strlen(log_entry), &bytes_written);
            if (res != FR_OK)
            {
                f_close(&log_file);
                g_internal_storage_state = INTERNAL_STORAGE_ACTIVE;
                return DATA_STORAGE_ERROR;
            }

            // 写入换行符
            res = f_write(&log_file, "\n", 1, &bytes_written);
            if (res != FR_OK)
            {
                f_close(&log_file);
                g_internal_storage_state = INTERNAL_STORAGE_ACTIVE;
                return DATA_STORAGE_ERROR;
            }
        }
    }

    f_sync(&log_file);
    f_close(&log_file);

    // 清空内部存储
    memset(g_internal_log_buffer, 0, sizeof(g_internal_log_buffer));
    g_internal_log_count = 0;
    g_internal_log_write_pos = 0;
    g_internal_storage_state = INTERNAL_STORAGE_DISABLED;

    return DATA_STORAGE_OK;
}

// 检查内部存储是否有数据
uint8_t internal_storage_has_data(void)
{
    return (g_internal_log_count > 0) ? 1 : 0;
}

// 检查SD卡状态并自动转移数据
void internal_storage_check_sd_and_transfer(void)
{
    // 如果内部存储有数据且当前状态为激活状态
    if (g_internal_storage_state == INTERNAL_STORAGE_ACTIVE && g_internal_log_count > 0)
    {
        // 尝试转移数据到SD卡
        data_storage_status_t result = internal_storage_transfer_to_sd();
        if (result == DATA_STORAGE_OK)
        {
            // 转移成功，切换到正常的SD卡存储模式
            my_printf(&huart1, "Internal log data transferred to SD card successfully\r\n");
        }
    }
}
