# 文件清单生成和配对分析报告

## 项目信息
- 项目A：成都纺织高等专科学校 (路径: sysFunction)
- 项目B：宜春学院 (路径: ../2025044054-code-宜春学院/2025044054-code-宜春学院/sysFunction)

## 文件统计概览

### 项目A文件清单 (成都纺织高等专科学校)
总计：25个文件 (12个.c文件 + 13个.h文件)

.c文件 (12个):
1. adc_app.c
2. btn_app.c
3. config_manager.c
4. data_storage.c
5. device_id.c
6. led_app.c
7. oled_app.c
8. rtc_app.c
9. sampling_control.c
10. scheduler.c
11. system_check.c
12. usart_app.c

.h文件 (13个):
1. adc_app.h
2. btn_app.h
3. config_manager.h
4. data_storage.h
5. device_id.h
6. led_app.h
7. mydefine.h
8. oled_app.h
9. rtc_app.h
10. sampling_control.h
11. scheduler.h
12. system_check.h
13. usart_app.h

### 项目B文件清单 (宜春学院)
总计：29个文件 (14个.c文件 + 14个.h文件 + 1个.md文件)

.c文件 (14个):
1. adc_app.c
2. btn_app.c
3. config_manager.c
4. data_storage.c
5. device_id.c
6. flash_app.c        [仅存在于项目B]
7. ini_parser.c       [仅存在于项目B]
8. led_app.c
9. oled_app.c
10. rtc_app.c
11. sampling_control.c
12. scheduler.c
13. system_check.c
14. usart_app.c

.h文件 (14个):
1. adc_app.h
2. btn_app.h
3. config_manager.h
4. data_storage.h
5. device_id.h
6. flash_app.h        [仅存在于项目B]
7. ini_parser.h       [仅存在于项目B]
8. led_app.h
9. mydefine.h
10. oled_app.h
11. rtc_app.h
12. sampling_control.h
13. scheduler.h
14. system_check.h
15. usart_app.h

其他文件 (1个):
1. README.md          [仅存在于项目B]

## 文件配对分析

### 完全匹配的文件对 (可进行查重比较)
总计：25对文件

.c文件配对 (12对):
1. adc_app.c ↔ adc_app.c
2. btn_app.c ↔ btn_app.c
3. config_manager.c ↔ config_manager.c
4. data_storage.c ↔ data_storage.c
5. device_id.c ↔ device_id.c
6. led_app.c ↔ led_app.c
7. oled_app.c ↔ oled_app.c
8. rtc_app.c ↔ rtc_app.c
9. sampling_control.c ↔ sampling_control.c
10. scheduler.c ↔ scheduler.c
11. system_check.c ↔ system_check.c
12. usart_app.c ↔ usart_app.c

.h文件配对 (13对):
1. adc_app.h ↔ adc_app.h
2. btn_app.h ↔ btn_app.h
3. config_manager.h ↔ config_manager.h
4. data_storage.h ↔ data_storage.h
5. device_id.h ↔ device_id.h
6. led_app.h ↔ led_app.h
7. mydefine.h ↔ mydefine.h
8. oled_app.h ↔ oled_app.h
9. rtc_app.h ↔ rtc_app.h
10. sampling_control.h ↔ sampling_control.h
11. scheduler.h ↔ scheduler.h
12. system_check.h ↔ system_check.h
13. usart_app.h ↔ usart_app.h

### 仅存在于项目B的文件 (无法配对)
总计：5个文件

1. flash_app.c
2. flash_app.h
3. ini_parser.c
4. ini_parser.h
5. README.md

### 仅存在于项目A的文件
总计：0个文件

## 配对统计摘要

- 可配对文件数：25对
- 项目A独有文件：0个
- 项目B独有文件：5个
- 配对成功率：100% (项目A的所有文件都在项目B中找到对应文件)
- 文件名匹配采用：大小写敏感的精确匹配

## 下一步分析计划

基于以上25对文件，将进行逐文件逐行的严格代码比较分析，采用大小写敏感的字符级匹配算法。
