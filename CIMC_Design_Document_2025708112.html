<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2025年CIMC工业嵌入式系统开发项目设计书</title>
    <style>
        body {
            font-family: "微软雅黑", "宋体", Arial, sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        h1 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 10px;
        }
        h2 {
            color: #34495e;
            font-size: 20px;
            border-bottom: 1px solid #bdc3c7;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        h3 {
            color: #7f8c8d;
            font-size: 16px;
            margin-top: 20px;
        }
        .info-table {
            width: 100%;
            margin-bottom: 20px;
        }
        .info-table td {
            padding: 5px 10px;
            border: 1px solid #ddd;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: "Consolas", "Monaco", monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        ul, ol {
            margin-left: 20px;
        }
        .section {
            margin-bottom: 25px;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            border-top: 1px solid #bdc3c7;
            padding-top: 20px;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>2025年CIMC工业嵌入式系统开发项目设计书</h1>
        <table class="info-table" style="margin: 20px auto; width: 60%;">
            <tr>
                <td><strong>参赛队伍：</strong></td>
                <td>成都纺织高等专科学校</td>
            </tr>
            <tr>
                <td><strong>队伍编号：</strong></td>
                <td>2025708112</td>
            </tr>
            <tr>
                <td><strong>开发环境：</strong></td>
                <td>Keil uVision5 V5.41.0.0</td>
            </tr>
            <tr>
                <td><strong>提交日期：</strong></td>
                <td>2025年6月18日</td>
            </tr>
        </table>
    </div>

    <div class="section">
        <h2>1. 项目概述</h2>
        
        <h3>1.1 项目背景</h3>
        <p>本项目是基于STM32F429微控制器的工业嵌入式数据采集与监控系统，专为工业现场的实时数据采集、处理和存储而设计。系统采用高精度ADC采样技术，结合多种存储方案和智能任务调度，实现了稳定可靠的工业数据采集解决方案。</p>

        <h3>1.2 项目目标</h3>
        <ul>
            <li>实现高精度12位ADC数据采集，支持DMA高效传输</li>
            <li>提供多层次数据存储方案（Flash + SD卡双重备份）</li>
            <li>建立完善的实时任务调度系统</li>
            <li>实现智能配置管理和参数持久化</li>
            <li>提供友好的人机交互界面和通信协议</li>
            <li>确保系统高可靠性和故障自恢复能力</li>
        </ul>

        <h3>1.3 技术特色</h3>
        <ul>
            <li><span class="highlight">高精度采样</span>：12位ADC分辨率，DMA传输，定时器触发</li>
            <li><span class="highlight">智能存储</span>：双重存储备份，自动故障切换</li>
            <li><span class="highlight">实时调度</span>：基于时间片的多任务调度系统</li>
            <li><span class="highlight">模块化设计</span>：清晰的分层架构，便于维护和扩展</li>
            <li><span class="highlight">完善自检</span>：全面的硬件和软件状态检测机制</li>
        </ul>
    </div>

    <div class="section">
        <h2>2. 系统架构设计</h2>
        
        <h3>2.1 硬件架构</h3>
        <div class="code-block">
STM32F429核心控制器
├── 数据采集模块
│   ├── ADC1 (12位精度，DMA传输)
│   ├── TIM3 (定时器触发采样)
│   └── 模拟信号调理电路
├── 存储子系统
│   ├── SPI Flash (GD25QXX系列，8MB)
│   ├── SD卡接口 (FATFS文件系统)
│   └── 内部Flash (配置参数存储)
├── 人机交互模块
│   ├── OLED显示屏 (128x64)
│   ├── 6路按键输入
│   └── 6路LED状态指示
├── 通信接口
│   ├── UART1 (串口通信)
│   └── DMA环形缓冲区
└── 时钟系统
    ├── RTC实时时钟
    └── 系统时基管理
        </div>

        <h3>2.2 软件架构</h3>
        <div class="code-block">
应用层 (Application Layer)
├── 采样控制 (sampling_control)
├── 数据存储 (data_storage)  
├── 配置管理 (config_manager)
├── 系统检测 (system_check)
├── 设备标识 (device_id)
└── 人机交互 (oled_app, btn_app, led_app)

中间件层 (Middleware Layer)
├── 任务调度器 (scheduler)
├── 文件系统 (FATFS)
├── Flash文件系统 (LittleFS)
├── 环形缓冲区 (ringbuffer)
└── 通信协议 (usart_app)

驱动层 (Driver Layer)
├── ADC驱动 (adc_app)
├── RTC驱动 (rtc_app)
├── Flash驱动 (GD25QXX)
├── OLED驱动 (oled)
└── GPIO驱动 (按键、LED)

硬件抽象层 (HAL Layer)
├── STM32F4xx HAL库
├── CMSIS核心库
└── 硬件寄存器封装
        </div>
    </div>

    <div class="section">
        <h2>3. 核心功能模块设计</h2>
        
        <h3>3.1 数据采集模块 (adc_app)</h3>
        <p><strong>功能描述：</strong></p>
        <ul>
            <li>12位ADC精度，支持连续采样和DMA传输</li>
            <li>定时器TIM3触发采样，确保采样时序精确</li>
            <li>2048点采样缓冲区，支持批量数据处理</li>
            <li>实时电压计算和数据预处理</li>
        </ul>

        <p><strong>技术实现：</strong></p>
        <div class="code-block">
// 核心采样配置
enum { ADC_BUFFER_SIZE = 2048 };
__IO uint32_t raw_sample_buffer[BUFFER_SIZE];
__IO float voltage_val;

// ADC初始化和DMA配置
void adc_tim_dma_init(void) {
    HAL_TIM_Base_Start(&htim3);
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)raw_sample_buffer, BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT);
}
        </div>

        <p><strong>关键参数：</strong></p>
        <ul>
            <li>ADC分辨率：12位 (4096级)</li>
            <li>参考电压：3.3V</li>
            <li>理论精度：0.8mV</li>
            <li>采样缓冲区：2048个采样点</li>
            <li>采样方式：定时器触发 + DMA传输</li>
        </ul>

        <h3>3.2 任务调度系统 (scheduler)</h3>
        <p><strong>功能描述：</strong></p>
        <ul>
            <li>基于时间片的协作式多任务调度</li>
            <li>支持不同优先级和执行周期的任务</li>
            <li>高效的任务管理和时间控制</li>
        </ul>

        <p><strong>调度策略：</strong></p>
        <div class="code-block">
static task_t scheduler_task[] = {
    {led_task, 1, 0},        // LED状态指示，1ms周期
    {adc_task, 100, 0},      // ADC数据采集，100ms周期  
    {btn_task, 5, 0},        // 按键扫描，5ms周期
    {uart_task, 5, 0},       // 串口通信，5ms周期
    {oled_task, 1, 0},       // 显示更新，1ms周期
    {sampling_task, 10, 0}   // 采样控制，10ms周期
};
        </div>
    </div>

    <div class="section">
        <h2>4. 性能指标与测试验证</h2>
        
        <h3>4.1 技术性能指标</h3>
        <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
            <tr style="background-color: #f8f9fa;">
                <th style="border: 1px solid #ddd; padding: 8px;">性能项目</th>
                <th style="border: 1px solid #ddd; padding: 8px;">技术指标</th>
            </tr>
            <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">ADC分辨率</td>
                <td style="border: 1px solid #ddd; padding: 8px;">12位 (4096级)</td>
            </tr>
            <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">采样精度</td>
                <td style="border: 1px solid #ddd; padding: 8px;">±0.1% (典型值)</td>
            </tr>
            <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">采样频率</td>
                <td style="border: 1px solid #ddd; padding: 8px;">可配置 5s/10s/15s</td>
            </tr>
            <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">响应时间</td>
                <td style="border: 1px solid #ddd; padding: 8px;">&lt;100ms</td>
            </tr>
            <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">Flash容量</td>
                <td style="border: 1px solid #ddd; padding: 8px;">8MB (SPI Flash)</td>
            </tr>
            <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">CPU利用率</td>
                <td style="border: 1px solid #ddd; padding: 8px;">&lt;60% (正常工作)</td>
            </tr>
        </table>
    </div>

    <div class="section">
        <h2>5. 创新点与技术特色</h2>
        
        <h3>5.1 技术创新</h3>
        <ol>
            <li><strong>智能存储切换技术</strong>
                <ul>
                    <li>SD卡故障时自动切换到Flash存储</li>
                    <li>数据同步和一致性保证</li>
                </ul>
            </li>
            <li><strong>高效任务调度系统</strong>
                <ul>
                    <li>基于时间片的协作式调度</li>
                    <li>动态任务优先级调整</li>
                </ul>
            </li>
            <li><strong>完善的自检机制</strong>
                <ul>
                    <li>全面的硬件状态检测</li>
                    <li>智能故障诊断</li>
                </ul>
            </li>
        </ol>

        <h3>5.2 设计特色</h3>
        <ul>
            <li><strong>高可靠性设计</strong>：双重存储备份，多重故障保护</li>
            <li><strong>用户友好界面</strong>：直观的OLED显示，简单的按键操作</li>
            <li><strong>灵活的配置管理</strong>：运行时参数修改，持久化配置存储</li>
            <li><strong>模块化软件架构</strong>：清晰的分层设计，松耦合模块接口</li>
        </ul>
    </div>

    <div class="section">
        <h2>6. 项目总结</h2>
        
        <h3>6.1 项目成果</h3>
        <p>本项目成功实现了基于STM32F429的工业嵌入式数据采集系统，具备高精度采样、双重存储、实时调度等核心功能。</p>

        <h3>6.2 技术优势</h3>
        <ol>
            <li><strong>高精度采样</strong>：12位ADC分辨率，DMA高效传输</li>
            <li><strong>高可靠性</strong>：双重存储备份，故障自动恢复</li>
            <li><strong>实时性强</strong>：优化的任务调度，低延迟响应</li>
            <li><strong>易于维护</strong>：模块化设计，清晰的软件架构</li>
        </ol>

        <h3>6.3 应用前景</h3>
        <p>本系统适用于工业过程监控、环境参数采集、设备状态监测等多种场景，具有广阔的应用前景。</p>
    </div>

    <div class="footer">
        <p><strong>项目团队：</strong> 成都纺织高等专科学校嵌入式开发组</p>
        <p><strong>文档版本：</strong> V1.0 &nbsp;&nbsp;&nbsp; <strong>完成日期：</strong> 2025年6月18日</p>
        <p><strong>声明：</strong> 本设计书所述技术方案和实现代码均为团队原创开发，符合比赛要求和学术诚信原则。</p>
    </div>
</body>
</html>
