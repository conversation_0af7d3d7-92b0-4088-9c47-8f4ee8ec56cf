#ifndef __DATA_STORAGE_H__
#define __DATA_STORAGE_H__

#include "stdint.h"
#include "ff.h"

#define DATA_STORAGE_MAX_RECORDS_PER_FILE 10

#define INTERNAL_LOG_MAX_ENTRIES 50
#define INTERNAL_LOG_ENTRY_SIZE 128
#define INTERNAL_LOG_BUFFER_SIZE (INTERNAL_LOG_MAX_ENTRIES * INTERNAL_LOG_ENTRY_SIZE)

typedef enum
{
    STORAGE_SAMPLE = 0,
    STORAGE_OVERLIMIT = 1,
    STORAGE_LOG = 2,
    STORAGE_HIDEDATA = 3,
    STORAGE_TYPE_COUNT = 4
} storage_type_t;

typedef enum
{
    DATA_STORAGE_OK = 0,
    DATA_STORAGE_ERROR,
    DATA_STORAGE_NO_SD,
    DATA_STORAGE_INVALID,
    DATA_STORAGE_FILE_ERROR,
    DATA_STORAGE_FULL
} data_storage_status_t;

typedef enum
{
    INTERNAL_STORAGE_DISABLED = 0,
    INTERNAL_STORAGE_ACTIVE,
    INTERNAL_STORAGE_TRANSFERRING
} internal_storage_state_t;

typedef struct
{
    char current_filename[32];
    uint8_t data_count;
    uint8_t file_opened;
    uint8_t file_exists;
} file_state_t;

data_storage_status_t data_storage_init(void);
data_storage_status_t data_storage_write_sample(float voltage);
data_storage_status_t data_storage_write_overlimit(float voltage, float limit);
data_storage_status_t data_storage_write_log(const char *operation);
data_storage_status_t data_storage_write_hidedata(float voltage, uint8_t is_overlimit);
data_storage_status_t data_storage_test(void);

data_storage_status_t generate_datetime_string(char *datetime_str);
data_storage_status_t generate_filename(storage_type_t type, char *filename);

data_storage_status_t internal_storage_init(void);
data_storage_status_t internal_storage_write_log(const char *operation);
data_storage_status_t internal_storage_transfer_to_sd(void);
uint8_t internal_storage_has_data(void);
void internal_storage_check_sd_and_transfer(void);

#endif
